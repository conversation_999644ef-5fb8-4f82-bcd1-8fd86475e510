#!/usr/bin/env python3
"""
Test script to verify device setup for multi-GPU configuration
"""
import torch
import argparse

def test_device_setup():
    parser = argparse.ArgumentParser()
    parser.add_argument("--device", default=1, type=int)
    parser.add_argument("--use_multi_gpu", default=True, type=bool, help="Use multiple GPUs with device_map='auto'")
    parser.add_argument("--use_fp16", default=False, type=bool, help="Use FP16 precision for multi-GPU")
    
    args = parser.parse_args()
    
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA device count: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
    
    # Device allocation logic (same as in the main scripts)
    if torch.cuda.is_available():
        if args.use_multi_gpu and torch.cuda.device_count() > 1:
            # Multi-GPU setup - use primary device for data loading
            device = torch.device("cuda:0")
            use_cuda = True
            print(f"Using Multi-GPU setup with {torch.cuda.device_count()} GPU(s)")
        else:
            # Single GPU setup
            if args.device >= 0 and args.device < torch.cuda.device_count():
                device = torch.device(f"cuda:{args.device}")
            else:
                device = torch.device("cuda:0")
            use_cuda = True
            print(f"Using single GPU: {device}")
    else:
        use_cuda = False
        device = torch.device("cpu")
        print("Using CPU")
    
    # Test tensor creation and device placement
    print(f"\nTesting tensor creation on device: {device}")
    test_tensor = torch.randn(2, 3).to(device)
    print(f"Test tensor device: {test_tensor.device}")
    print(f"Test tensor shape: {test_tensor.shape}")
    
    # Test simple model with DataParallel if multi-GPU
    if use_cuda and args.use_multi_gpu and torch.cuda.device_count() > 1:
        print(f"\nTesting DataParallel setup...")
        model = torch.nn.Linear(3, 1).to(device)
        model = torch.nn.DataParallel(model)
        print(f"DataParallel model created successfully")
        
        # Test forward pass
        output = model(test_tensor)
        print(f"Forward pass successful, output device: {output.device}")
    
    print("\nDevice setup test completed successfully!")

if __name__ == "__main__":
    test_device_setup()
